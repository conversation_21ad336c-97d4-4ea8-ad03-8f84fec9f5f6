import { UserEntity } from "./entities/user";
import { PhysicalEntity } from "./entities/physicial";
import { repo, URPC } from "@unilab/urpc";

URPC.init({
  baseUrl: "http://localhost:3000",
  timeout: 30000, // Match server timeout
});

// Test 1: Fetch User entities (mock data)
const fetchUser = async () => {
  console.log("🧪 Testing User Entity (mock data)...");
  try {
    const data = await repo<UserEntity>({
      entity: "user",
      source: "mock",
    }).findMany({
      where: {
        id: "1",
      },
    });
    console.log("[1] User Entity =>", JSON.stringify(data, null, 2));
  } catch (error) {
    console.error("❌ Error fetching User:", error);
  }
};

// Test 2: Fetch Physical entities (JSON data via DuckDB adapter)
const fetchPhysical = async () => {
  console.log("🧪 Testing Physical Entity (JSON source via DuckDB)...");
  try {
    const data = await repo<PhysicalEntity>({
      entity: "PhysicalEntity",
      source: "json",
    }).findMany({
      limit: 5,
      offset: 0,
    });
    console.log("[2] Physical Entity =>", JSON.stringify(data, null, 2));

    // Test specific camera by ID
    if (data.length > 0) {
      console.log("🔍 Testing specific camera lookup...");
      const specificCamera = await repo<PhysicalEntity>({
        entity: "PhysicalEntity",
        source: "json",
      }).findMany({
        where: {
          id: data[0].id, // Use first camera's ID
        },
        limit: 1,
      });
      console.log("[3] Specific Camera =>", JSON.stringify(specificCamera, null, 2));
    }
  } catch (error) {
    console.error("❌ Error fetching Physical Entity:", error);
  }
};

// Test 3: Test different query parameters for Physical entities
const testPhysicalQueries = async () => {
  console.log("🧪 Testing Physical Entity with different parameters...");
  try {
    // Test with different limits
    const limitedData = await repo<PhysicalEntity>({
      entity: "PhysicalEntity",
      source: "json",
    }).findMany({
      limit: 2,
      offset: 1,
    });
    console.log("[4] Limited Physical Entities =>", JSON.stringify(limitedData, null, 2));

    // Test camera type filtering (if supported)
    const cameraData = await repo<PhysicalEntity>({
      entity: "PhysicalEntity",
      source: "json",
    }).findMany({
      where: {
        type: "camera",
      },
      limit: 3,
    });
    console.log("[5] Camera Type Entities =>", JSON.stringify(cameraData, null, 2));
  } catch (error) {
    console.error("❌ Error in Physical Entity queries:", error);
  }
};

// Run all tests
const runAllTests = async () => {
  console.log("🚀 Starting client tests based on server configuration...\n");

  await fetchUser();
  console.log("\n" + "=".repeat(50) + "\n");

  await fetchPhysical();
  console.log("\n" + "=".repeat(50) + "\n");

  await testPhysicalQueries();

  console.log("\n✅ All tests completed!");
};

runAllTests();
