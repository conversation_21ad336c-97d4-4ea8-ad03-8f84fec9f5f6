import { URPC } from "@unilab/urpc-hono";
import { UserEntity } from "./entities/user";
import { Plugin } from "@unilab/urpc-core";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { PhysicalEntity } from "./entities/physicial";
import { PhysicalAdapter } from "./adapters/physicial";

const MyPlugin: Plugin = {
  entities: [UserEntity, PhysicalEntity],
  adapters: [
    {
      source: 'json',
      entity: 'PhysicalEntity',
      adapter: new PhysicalAdapter(),
    }
  ] 
};

const app = new Hono();

app.use(cors())
URPC.init({
  plugins: [MyPlugin],
  app,
});

const server = {
  port: 3000,
  timeout: 30000,
  fetch: app.fetch,
};

console.log(`🚀 Server running on http://localhost:${server.port}`);

export default server;
