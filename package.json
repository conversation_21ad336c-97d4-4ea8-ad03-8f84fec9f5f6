{"name": "hono-basic-example", "version": "1.0.0", "description": "Basic Hono example using @unilab/urpc-hono and @unilab/urpc", "main": "server.ts", "scripts": {"dev": "bun run server.ts", "start": "bun run server.ts", "client": "bun run client.ts", "deploy": "npx wrangler deploy", "deploy:dev": "npx wrangler deploy --env dev", "deploy:production": "npx wrangler deploy --env production", "preview": "npx wrangler dev"}, "dependencies": {"@hono/node-server": "^1.12.2", "@unilab/uniweb3": "^0.0.12", "@unilab/urpc": "^0.0.15", "@unilab/urpc-adapters": "^0.0.12", "@unilab/urpc-core": "^0.0.10", "@unilab/urpc-hono": "^0.0.13", "duckdb": "^1.3.2", "hono": "^4.7.11"}, "devDependencies": {"@types/bun": "^1.1.12", "typescript": "^5.3.0", "wrangler": "^3.0.0"}, "keywords": ["hono", "unilab", "api", "example"], "author": "", "license": "MIT"}