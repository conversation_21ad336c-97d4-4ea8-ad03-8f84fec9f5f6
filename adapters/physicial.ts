import { BaseAdapter, FindManyArgs } from "@unilab/urpc-core";
import { PhysicalEntity } from "../entities/physicial";
import duckdb from "duckdb";

export class PhysicalAdapter extends BaseAdapter<PhysicalEntity> {
  findMany(
    args?: FindManyArgs<PhysicalEntity> | undefined
  ): Promise<PhysicalEntity[]> {
    const JSON_URL = `https://huggingface.co/datasets/nvidia/PhysicalAI-SmartSpaces/resolve/main/MTMC_Tracking_2024/test/scene_061/calibration_2025_format.json`;

    const limit = args?.limit || 10;
    const offset = args?.offset || 0;

    const db = new duckdb.Database(":memory:");
    const query = `
    SELECT
    sensor ->> 'type' AS type,
    sensor ->> 'id' AS id,
    sensor ->> 'intrinsicMatrix' AS intrinsicMatrix,
    sensor ->> 'extrinsicMatrix' AS extrinsicMatrix,
    sensor ->> 'cameraMatrix' AS cameraMatrix,
    sensor ->> 'homography' AS homography,
    sensor -> 'attributes' AS attributes
    FROM (
    SELECT UNNEST(sensor.sensors) AS sensor
    FROM read_json_auto('${JSON_URL}') sensor
    ) list
    LIMIT ${limit}
    OFFSET ${offset}
    `;

    const items = [] as PhysicalEntity[];
    db.all(query, (err: any, rows: any) => {
      if (err) {
        console.error("DuckDB error:", err);
        return;
      }
      items.push(...rows.map((row: any) => {
        const entity = new PhysicalEntity();
        entity.type = row.type;
        entity.id = row.id;
        entity.intrinsicMatrix = JSON.parse(row.intrinsicMatrix);
        entity.extrinsicMatrix = JSON.parse(row.extrinsicMatrix);
        entity.cameraMatrix = JSON.parse(row.cameraMatrix);
        entity.homography = JSON.parse(row.homography);
        entity.attributes = JSON.parse(row.attributes);
        return entity;
      }));
      db.close();
    });

    return Promise.resolve(items);
  }
}
