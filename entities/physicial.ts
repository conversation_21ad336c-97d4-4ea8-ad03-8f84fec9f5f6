export class PhysicalEntity {
  type: string = "camera";
  id: string = "Camera_0535";
  intrinsicMatrix: number[][] = [
    [1662.8, 0.0, 960.0],
    [0.0, 1662.8, 540.0],
    [0.0, 0.0, 1.0],
  ];
  extrinsicMatrix: number[][] = [
    [
      0.9397257080761404, -0.34203217216764753, 1.3725152885734296e-8,
      29.052687001648025,
    ],
    [
      -0.1824892084237681, -0.5013850338284965, -0.8458020748842563,
      11.504361738389218,
    ],
    [
      0.2892867681628683, 0.7948088742721874, -0.5335728074525293,
      -5.1546855731292816,
    ],
  ];
  cameraMatrix: number[][] = [
    [
      -357.01328019279447, -37.69103287962329, 99.37170076918619,
      -8411.824384045456,
    ],
    [
      28.562013894033516, 78.4735046210543, 328.7356681802806,
      -3171.0804193980302,
    ],
    [-0.05612112786682535, -0.15419153370196326, 0.10351219291317716, 1.0],
  ];
  homography: number[][] = [
    [-357.01328019279447, -37.69103287962329, -8411.824384045456],
    [28.562013894033516, 78.4735046210543, -3171.0804193980302],
    [-0.05612112786682535, -0.15419153370196326, 1.0],
  ];
  attributes: { name: string; value: string }[] = [
    { name: "fps", value: "30" },
    { name: "frameWidth", value: "1920" },
    { name: "frameHeight", value: "1080" },
  ];
}
